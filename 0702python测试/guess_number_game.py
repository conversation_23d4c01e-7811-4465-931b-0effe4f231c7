#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的猜数字游戏
作者：AI助手
日期：2024
"""

import random
import sys

def main():
    print("🎮 欢迎来到猜数字游戏！")
    print("=" * 30)
    
    while True:
        # 选择游戏难度
        print("\n请选择游戏难度：")
        print("1. 简单 (1-10)")
        print("2. 中等 (1-50)")
        print("3. 困难 (1-100)")
        print("4. 退出游戏")
        
        try:
            choice = input("\n请输入选择 (1-4): ").strip()
            
            if choice == '4':
                print("👋 谢谢游戏，再见！")
                sys.exit(0)
            elif choice == '1':
                max_num = 10
                max_attempts = 3
            elif choice == '2':
                max_num = 50
                max_attempts = 7
            elif choice == '3':
                max_num = 100
                max_attempts = 10
            else:
                print("❌ 无效选择，请重新输入！")
                continue
                
        except KeyboardInterrupt:
            print("\n\n👋 游戏被中断，再见！")
            sys.exit(0)
        
        # 开始游戏
        target_number = random.randint(1, max_num)
        attempts = 0
        
        print(f"\n🎯 我已经想好了一个 1 到 {max_num} 之间的数字")
        print(f"📝 你有 {max_attempts} 次机会猜中它！")
        
        while attempts < max_attempts:
            try:
                guess = input(f"\n第 {attempts + 1} 次猜测，请输入你的数字: ").strip()
                
                # 检查输入是否为数字
                if not guess.isdigit():
                    print("❌ 请输入一个有效的数字！")
                    continue
                    
                guess_num = int(guess)
                attempts += 1
                
                if guess_num < 1 or guess_num > max_num:
                    print(f"❌ 请输入 1 到 {max_num} 之间的数字！")
                    attempts -= 1  # 不计入尝试次数
                    continue
                
                if guess_num == target_number:
                    print(f"🎉 恭喜你！猜对了！答案就是 {target_number}")
                    print(f"👏 你用了 {attempts} 次就猜中了！")
                    
                    # 显示评价
                    if attempts == 1:
                        print("🏆 Amazing! 一次就中！")
                    elif attempts <= max_attempts // 2:
                        print("😄 Very Good! 很棒的表现！")
                    else:
                        print("😊 Good! 不错的尝试！")
                    break
                    
                elif guess_num < target_number:
                    remaining = max_attempts - attempts
                    if remaining > 0:
                        print(f"📈 太小了！再试试更大的数字 (还有 {remaining} 次机会)")
                    
                else:
                    remaining = max_attempts - attempts
                    if remaining > 0:
                        print(f"📉 太大了！再试试更小的数字 (还有 {remaining} 次机会)")
                        
            except KeyboardInterrupt:
                print("\n\n👋 游戏被中断，再见！")
                sys.exit(0)
            except ValueError:
                print("❌ 请输入一个有效的数字！")
                attempts -= 1  # 不计入尝试次数
        
        # 游戏结束检查
        if attempts >= max_attempts and guess_num != target_number:
            print(f"\n💀 游戏结束！你用完了所有 {max_attempts} 次机会")
            print(f"🔍 正确答案是: {target_number}")
        
        # 询问是否再玩一次
        while True:
            try:
                play_again = input("\n🔄 想再玩一次吗？(y/n): ").strip().lower()
                if play_again in ['y', 'yes', '是', 'y']:
                    break
                elif play_again in ['n', 'no', '否', 'n']:
                    print("👋 谢谢游戏，再见！")
                    sys.exit(0)
                else:
                    print("❌ 请输入 y (是) 或 n (否)")
            except KeyboardInterrupt:
                print("\n\n👋 游戏被中断，再见！")
                sys.exit(0)

if __name__ == "__main__":
    main() 