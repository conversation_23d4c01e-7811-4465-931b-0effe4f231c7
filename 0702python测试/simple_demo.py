#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的Python演示脚本
展示基本功能：计算、文件操作、时间等
"""

import datetime
import os
import random
import math

def print_separator(title=""):
    """打印分隔线"""
    if title:
        print(f"\n{'='*10} {title} {'='*10}")
    else:
        print("="*40)

def basic_calculations():
    """基本计算演示"""
    print_separator("数学计算演示")
    
    # 随机生成两个数字
    a = random.randint(1, 100)
    b = random.randint(1, 100)
    
    print(f"随机数字 A: {a}")
    print(f"随机数字 B: {b}")
    print(f"A + B = {a + b}")
    print(f"A - B = {a - b}")
    print(f"A × B = {a * b}")
    print(f"A ÷ B = {a / b:.2f}")
    print(f"A 的平方根 = {math.sqrt(a):.2f}")
    print(f"A 的 B 次方 = {a ** b:,}")

def system_info():
    """系统信息展示"""
    print_separator("系统信息")
    
    current_time = datetime.datetime.now()
    print(f"📅 当前时间: {current_time.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"📁 当前目录: {os.getcwd()}")
    print(f"🐍 Python 脚本: {__file__}")
    print(f"👤 用户环境: {os.environ.get('USER', '未知')}")

def text_processing():
    """文本处理演示"""
    print_separator("文本处理演示")
    
    sample_text = "Hello, 这是一个Python演示脚本! 🚀"
    print(f"原文本: {sample_text}")
    print(f"大写: {sample_text.upper()}")
    print(f"小写: {sample_text.lower()}")
    print(f"字符数: {len(sample_text)}")
    print(f"单词数: {len(sample_text.split())}")
    print(f"反转: {sample_text[::-1]}")

def list_operations():
    """列表操作演示"""
    print_separator("列表操作演示")
    
    # 生成随机数列表
    numbers = [random.randint(1, 100) for _ in range(10)]
    print(f"随机数列表: {numbers}")
    print(f"最大值: {max(numbers)}")
    print(f"最小值: {min(numbers)}")
    print(f"平均值: {sum(numbers)/len(numbers):.2f}")
    print(f"排序后: {sorted(numbers)}")
    print(f"去重后: {sorted(set(numbers))}")

def file_operations():
    """文件操作演示"""
    print_separator("文件操作演示")
    
    # 创建一个临时文件
    filename = "temp_demo.txt"
    content = f"""这是一个临时演示文件
创建时间: {datetime.datetime.now()}
随机数: {random.randint(1000, 9999)}
Python 很有趣! 🐍
"""
    
    # 写入文件
    with open(filename, 'w', encoding='utf-8') as f:
        f.write(content)
    print(f"✅ 已创建文件: {filename}")
    
    # 读取文件
    with open(filename, 'r', encoding='utf-8') as f:
        file_content = f.read()
    print(f"📖 文件内容:\n{file_content}")
    
    # 文件信息
    file_size = os.path.getsize(filename)
    print(f"📊 文件大小: {file_size} 字节")
    
    # 清理临时文件
    os.remove(filename)
    print(f"🗑️ 已删除临时文件: {filename}")

def fun_ascii_art():
    """有趣的ASCII艺术"""
    print_separator("ASCII 艺术")
    
    python_art = """
    🐍 Python 很棒!
    
      ___                   _   _                 
     / _ \\  _   _  ___  ___| |_(_) ___  _ __  ___ 
    | | | || | | |/ _ \\/ __| __| |/ _ \\| '_ \\/ __|
    | |_| || |_| |  __/\\__ \\ |_| | (_) | | | \\__ \\
     \\___/  \\__,_|\\___||___/\\__|_|\\___/|_| |_|___/
    
    """
    print(python_art)

def main():
    """主函数"""
    print("🚀 欢迎使用 Python 演示脚本!")
    print("这个脚本展示了 Python 的基本功能")
    
    # 运行各种演示
    basic_calculations()
    system_info()
    text_processing()
    list_operations()
    file_operations()
    fun_ascii_art()
    
    print_separator("演示完成")
    print("🎉 所有演示都已完成!")
    print("💡 你可以修改这个脚本来学习更多 Python 功能")
    print("\n📁 项目中的文件:")
    for file in os.listdir('.'):
        if file.endswith('.py'):
            print(f"  🐍 {file}")

if __name__ == "__main__":
    main() 