#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel文件处理脚本
将Excel数据转换为适合导入MaxCompute临时表的格式
"""

import pandas as pd
import json
import sys
import os

def read_excel_file(file_path):
    """读取Excel文件"""
    try:
        # 读取Excel文件
        df = pd.read_excel(file_path)
        print(f"成功读取Excel文件: {file_path}")
        print(f"数据形状: {df.shape}")
        print(f"列名: {list(df.columns)}")
        print("\n前5行数据:")
        print(df.head())
        return df
    except Exception as e:
        print(f"读取Excel文件失败: {e}")
        return None

def process_data_for_maxcompute(df):
    """
    处理数据以适配MaxCompute表结构

    表结构:
    - id: bigint(20) NOT NULL AUTO_INCREMENT (自动生成)
    - code_tplid: bigint(20) DEFAULT NULL (设为0)
    - org_id: bigint(20) DEFAULT NULL (设为0)
    - tpl_fields_num: bigint(20) DEFAULT NULL (Excel的字段个数)
    - ass_id: bigint(20) DEFAULT NULL (设为空)
    - origin_style_tplid: bigint(20) DEFAULT NULL (模板编码去除B之后的数字)
    - variable_ass_field: text (可以为空)
    - decoded_field: text (字段内容)
    - is_public: tinyint(4) DEFAULT NULL (设为1)

    Excel列对应关系:
    - 模板编码: 用于提取origin_style_tplid
    - 字段个数: 对应tpl_fields_num
    - 字段: 对应decoded_field
    """

    processed_data = []

    for index, row in df.iterrows():
        # 提取模板编码中的数字部分 (去除B前缀)
        template_code = str(row['模板编码']).strip()
        origin_style_tplid = None
        if template_code.startswith('B'):
            try:
                origin_style_tplid = int(template_code[1:])  # 去除B，转换为数字
            except ValueError:
                print(f"警告: 无法解析模板编码 {template_code}")
                origin_style_tplid = None

        # 获取字段个数
        tpl_fields_num = None
        if pd.notna(row['字段个数']):
            try:
                tpl_fields_num = int(row['字段个数'])
            except ValueError:
                print(f"警告: 无法解析字段个数 {row['字段个数']}")

        # 获取字段内容
        decoded_field = None
        if pd.notna(row['字段']) and str(row['字段']).strip():
            decoded_field = str(row['字段']).strip()

        # 构建记录
        record = {
            'code_tplid': 0,
            'org_id': 0,
            'tpl_fields_num': tpl_fields_num,
            'ass_id': None,  # 空值
            'origin_style_tplid': origin_style_tplid,
            'variable_ass_field': None,  # 可以为空
            'decoded_field': decoded_field,
            'is_public': 1
        }

        processed_data.append(record)

    return processed_data

def generate_sql_insert(processed_data, table_name="idl_max25703_style_all_data"):
    """生成SQL插入语句"""
    
    sql_statements = []
    
    for record in processed_data:
        # 处理NULL值
        values = []
        for key in ['code_tplid', 'org_id', 'tpl_fields_num', 'ass_id', 
                   'origin_style_tplid', 'variable_ass_field', 'decoded_field', 'is_public']:
            value = record[key]
            if value is None:
                values.append('NULL')
            elif isinstance(value, str):
                # 转义单引号
                escaped_value = value.replace("'", "''")
                values.append(f"'{escaped_value}'")
            else:
                values.append(str(value))
        
        sql = f"""INSERT INTO {table_name} 
(code_tplid, org_id, tpl_fields_num, ass_id, origin_style_tplid, variable_ass_field, decoded_field, is_public) 
VALUES ({', '.join(values)});"""
        
        sql_statements.append(sql)
    
    return sql_statements

def main():
    # Excel文件路径
    excel_file = "/Users/<USER>/Documents/script/样例标签处理0707/默认标签.xlsx"
    
    if not os.path.exists(excel_file):
        print(f"文件不存在: {excel_file}")
        return
    
    # 读取Excel文件
    df = read_excel_file(excel_file)
    if df is None:
        return
    
    # 处理数据
    processed_data = process_data_for_maxcompute(df)
    print(f"\n处理完成，共生成 {len(processed_data)} 条记录")
    
    # 生成SQL语句
    sql_statements = generate_sql_insert(processed_data)
    
    # 保存SQL文件
    output_file = "maxcompute_insert.sql"
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write("-- MaxCompute插入语句\n")
        f.write("-- 生成时间: " + pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S') + "\n\n")
        for sql in sql_statements:
            f.write(sql + "\n\n")
    
    print(f"SQL文件已保存: {output_file}")
    
    # 显示前几条记录的示例
    print("\n前3条记录示例:")
    for i, record in enumerate(processed_data[:3]):
        print(f"记录 {i+1}:")
        for key, value in record.items():
            print(f"  {key}: {value}")
        print()

if __name__ == "__main__":
    main()
