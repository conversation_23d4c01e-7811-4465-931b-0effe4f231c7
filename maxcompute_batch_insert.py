#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成MaxCompute批量插入脚本
"""

import pandas as pd
import os

def generate_maxcompute_batch_insert():
    """生成MaxCompute批量插入语句"""
    
    # Excel文件路径
    excel_file = "/Users/<USER>/Documents/script/样例标签处理0707/默认标签.xlsx"
    
    if not os.path.exists(excel_file):
        print(f"文件不存在: {excel_file}")
        return
    
    # 读取Excel文件
    df = pd.read_excel(excel_file)
    print(f"成功读取Excel文件，共 {len(df)} 行数据")
    
    # 生成VALUES子句
    values_list = []
    
    for index, row in df.iterrows():
        # 提取模板编码中的数字部分
        template_code = str(row['模板编码']).strip()
        origin_style_tplid = 'NULL'
        if template_code.startswith('B'):
            try:
                origin_style_tplid = str(int(template_code[1:]))
            except ValueError:
                origin_style_tplid = 'NULL'
        
        # 获取字段个数
        tpl_fields_num = 'NULL'
        if pd.notna(row['字段个数']):
            try:
                tpl_fields_num = str(int(row['字段个数']))
            except ValueError:
                tpl_fields_num = 'NULL'
        
        # 获取字段内容并转义单引号
        decoded_field = 'NULL'
        if pd.notna(row['字段']) and str(row['字段']).strip():
            field_content = str(row['字段']).strip()
            # 转义单引号
            field_content = field_content.replace("'", "''")
            decoded_field = f"'{field_content}'"
        
        # 构建VALUES行
        values_row = f"(0, 0, {tpl_fields_num}, NULL, {origin_style_tplid}, NULL, {decoded_field}, 1)"
        values_list.append(values_row)
    
    # 生成完整的INSERT语句
    table_name = "idl_max25703_style_all_data"
    columns = "(code_tplid, org_id, tpl_fields_num, ass_id, origin_style_tplid, variable_ass_field, decoded_field, is_public)"
    
    # 分批插入，每批100条记录
    batch_size = 100
    sql_statements = []
    
    for i in range(0, len(values_list), batch_size):
        batch_values = values_list[i:i+batch_size]
        values_clause = ",\n    ".join(batch_values)
        
        sql = f"""INSERT INTO {table_name} 
{columns} 
VALUES 
    {values_clause};"""
        
        sql_statements.append(sql)
    
    # 保存SQL文件
    output_file = "maxcompute_batch_insert.sql"
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write("-- MaxCompute批量插入语句\n")
        f.write(f"-- 生成时间: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"-- 总记录数: {len(df)}\n")
        f.write(f"-- 批次数: {len(sql_statements)}\n\n")
        
        for i, sql in enumerate(sql_statements, 1):
            f.write(f"-- 批次 {i}\n")
            f.write(sql + "\n\n")
    
    print(f"批量插入SQL文件已保存: {output_file}")
    print(f"共生成 {len(sql_statements)} 个批次，每批最多 {batch_size} 条记录")
    
    # 生成CSV文件用于数据导入
    csv_data = []
    for index, row in df.iterrows():
        template_code = str(row['模板编码']).strip()
        origin_style_tplid = None
        if template_code.startswith('B'):
            try:
                origin_style_tplid = int(template_code[1:])
            except ValueError:
                origin_style_tplid = None
        
        tpl_fields_num = None
        if pd.notna(row['字段个数']):
            try:
                tpl_fields_num = int(row['字段个数'])
            except ValueError:
                tpl_fields_num = None
        
        decoded_field = None
        if pd.notna(row['字段']) and str(row['字段']).strip():
            decoded_field = str(row['字段']).strip()
        
        csv_row = {
            'code_tplid': 0,
            'org_id': 0,
            'tpl_fields_num': tpl_fields_num,
            'ass_id': None,
            'origin_style_tplid': origin_style_tplid,
            'variable_ass_field': None,
            'decoded_field': decoded_field,
            'is_public': 1
        }
        csv_data.append(csv_row)
    
    # 保存CSV文件
    csv_df = pd.DataFrame(csv_data)
    csv_file = "maxcompute_data.csv"
    csv_df.to_csv(csv_file, index=False, encoding='utf-8')
    print(f"CSV数据文件已保存: {csv_file}")
    
    # 显示统计信息
    print(f"\n数据统计:")
    print(f"- 总记录数: {len(df)}")
    print(f"- 模板编码范围: {df['模板编码'].min()} - {df['模板编码'].max()}")
    print(f"- 字段个数范围: {df['字段个数'].min()} - {df['字段个数'].max()}")
    print(f"- 场景类型: {df['场景'].unique()}")

if __name__ == "__main__":
    generate_maxcompute_batch_insert()
