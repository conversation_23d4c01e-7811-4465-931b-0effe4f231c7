# Excel数据处理说明

## 概述
本项目将Excel文件 `默认标签.xlsx` 中的数据转换为适合导入MaxCompute临时表 `idl_max25703_style_all_data` 的格式。

## 原始数据结构
Excel文件包含以下列：
- **模板编码**: 如B502, B509等
- **模板名称**: 模板的名称描述
- **字段个数**: 该模板包含的字段数量
- **场景**: 使用场景分类
- **字段**: 具体的字段内容

## 目标表结构
MaxCompute表 `idl_max25703_style_all_data` 结构：
```sql
CREATE TABLE `idl_max25703_style_all_data` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `code_tplid` bigint(20) DEFAULT NULL,
  `org_id` bigint(20) DEFAULT NULL,
  `tpl_fields_num` bigint(20) DEFAULT NULL,
  `ass_id` bigint(20) DEFAULT NULL,
  `origin_style_tplid` bigint(20) DEFAULT NULL,
  `variable_ass_field` text,
  `decoded_field` text,
  `is_public` tinyint(4) DEFAULT NULL COMMENT '是否是公共模板',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3217827 DEFAULT CHARSET=utf8mb4;
```

## 字段映射关系
| Excel列 | 目标表字段 | 处理规则 |
|---------|------------|----------|
| 模板编码 | origin_style_tplid | 去除B前缀，转换为数字 (如B502 → 502) |
| 字段个数 | tpl_fields_num | 直接使用Excel中的数值 |
| 字段 | decoded_field | 直接使用字段内容 |
| - | code_tplid | 固定值：0 |
| - | org_id | 固定值：0 |
| - | ass_id | 固定值：NULL |
| - | variable_ass_field | 固定值：NULL |
| - | is_public | 固定值：1 |

## 生成的文件

### 1. process_excel.py
基础处理脚本，逐条生成INSERT语句。

### 2. maxcompute_batch_insert.py
批量处理脚本，生成批量INSERT语句和CSV文件。

### 3. maxcompute_insert.sql
单条INSERT语句文件，包含156条独立的INSERT语句。

### 4. maxcompute_batch_insert.sql
批量INSERT语句文件，将数据分为2个批次，每批最多100条记录。

### 5. maxcompute_data.csv
CSV格式的数据文件，可用于数据导入工具。

## 数据统计
- **总记录数**: 156条
- **模板编码范围**: B126 - B717
- **字段个数范围**: 1 - 18个字段
- **场景类型**: 19种不同场景

## 使用方法

### 方法1：使用SQL语句直接插入
```bash
# 使用单条INSERT语句
mysql -u username -p database_name < maxcompute_insert.sql

# 或使用批量INSERT语句（推荐）
mysql -u username -p database_name < maxcompute_batch_insert.sql
```

### 方法2：使用CSV文件导入
```bash
# 使用MySQL LOAD DATA命令
LOAD DATA INFILE 'maxcompute_data.csv' 
INTO TABLE idl_max25703_style_all_data 
FIELDS TERMINATED BY ',' 
ENCLOSED BY '"' 
LINES TERMINATED BY '\n' 
IGNORE 1 ROWS
(code_tplid, org_id, tpl_fields_num, @ass_id, origin_style_tplid, @variable_ass_field, decoded_field, is_public)
SET ass_id = NULLIF(@ass_id, ''), variable_ass_field = NULLIF(@variable_ass_field, '');
```

### 方法3：使用MaxCompute客户端
如果是MaxCompute环境，可以使用tunnel命令：
```bash
tunnel upload maxcompute_data.csv idl_max25703_style_all_data -fd "," -rd "\n" -h
```

## 注意事项

1. **字符编码**: 所有文件都使用UTF-8编码，确保中文字符正确显示。

2. **单引号转义**: SQL语句中的单引号已经正确转义（' → ''）。

3. **NULL值处理**: 空值在SQL中表示为NULL，在CSV中表示为空字符串。

4. **批量插入**: 推荐使用批量INSERT语句，性能更好。

5. **数据验证**: 建议在正式导入前先在测试环境验证数据正确性。

## 示例数据
```sql
INSERT INTO idl_max25703_style_all_data 
(code_tplid, org_id, tpl_fields_num, ass_id, origin_style_tplid, variable_ass_field, decoded_field, is_public) 
VALUES (0, 0, 4, NULL, 502, NULL, '英语培训课件; 包含初三英语试卷，中期听力试卷等资料; 扫描二维码获取文件; 图片: https://meihua.oss-cn-hangzhou.aliyuncs.com/cli/images/beautify/new/logo/公众号-0210.png', 1);
```

## 脚本运行方法
```bash
# 基础处理
python3 process_excel.py

# 批量处理（推荐）
python3 maxcompute_batch_insert.py
```

## 依赖包
```bash
pip install pandas openpyxl
```
